import { MastraClient } from "@mastra/client-js";
import { RuntimeContext } from "@mastra/core/runtime-context";
import { mastra } from "../mastra";
import { getMoolyAccount } from "./postgres/chatbot.service";
import {
  validateAgentResponse,
  isRetryableError,
  createFallbackResponse,
  logAgentMetrics,
  calculateRetryDelay
} from "../utils/agent.utils";
import { supabase, supabaseAdmin } from "../config/supabase";
import { z } from "zod";
import { GoogleGenerativeAIProviderOptions } from "@ai-sdk/google";

/**
 * Interface cho thông tin chatbot từ database
 */
interface ChatbotInfo {
  channel_type: string;
  instruction: string | null;
  type?: 'sale' | 'rag_bot' | 'sale_bot';
}

/**
 * Interface cho chatbot configuration từ Supabase
 */
interface ChatbotConfiguration {
  id: string;
  name: string;
  type: 'sale' | 'rag_bot' | 'sale_bot';
  is_active: boolean;
  instruction: string | null;
  tenant_id: string;
  delay_time: number;
  enabled_features: string[];
  model_config: {
    model: string;
    provider: string;
    max_tokens: number;
    temperature: number;
  };
}

/**
 * Retry configuration
 */
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 1,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
};

/**
 * Sleep function for retry delays
 */
const sleep = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Tạo phản hồi từ agent với retry logic
 *
 * Hỗ trợ hai chế độ:
 * 1. Sử dụng trực tiếp mastra instance (không có telemetry)
 * 2. Sử dụng MastraClient để kết nối đến server (có telemetry)
 *
 * @param message Nội dung tin nhắn
 * @param threadId ID của thread (cuộc hội thoại)
 * @param resourceId ID của resource (người dùng)
 * @param botId ID của chatbot (từ database)
 * @param tenantId ID của tenant (từ database)
 * @param chatbotInfo Thông tin chi tiết về chatbot (tùy chọn)
 * @param accoutnId Account ID
 * @param conversationId Conversation ID
 * @param contactName Tên khách hàng từ webhook
 * @param retryConfig Cấu hình retry (tùy chọn)
 */
export const generateAgentResponse = async (
  message: any,
  threadId?: string,
  resourceId?: string,
  botId?: string,
  tenantId?: string,
  chatbotInfo?: ChatbotInfo | null,
  accoutnId?: string,
  conversationId?: string,
  contactName?: string,
  retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG
) => {
  // Sử dụng thông tin từ database nếu có, nếu không sử dụng giá trị mặc định
  const botIdToUse = botId || "3a38a49b-6ea7-4942-95bf-fc8e612c195a";
  const tenantIdToUse = tenantId || "4ebbbb49-db73-4420-acac-96aaf1670aef";
  const threadIdToUse = threadId || "new_thread";
  const resourceIdToUse = resourceId || "anonymous";

  // Telemetry metadata
  const telemetryMetadata = {
    userId: resourceIdToUse,
    sessionId: threadIdToUse,
    tags: ["tho-tran-shop", "customer-service"],
  };

  // Thêm thông tin về chatbot vào metadata nếu có
  if (chatbotInfo) {
    telemetryMetadata.tags.push(
      `channel:${chatbotInfo?.channel_type || "unknown"}`
    );
    if (chatbotInfo.instruction) {
      telemetryMetadata.tags.push(`bot:${chatbotInfo.instruction}`);
    }
  }
  let apiToken = "";

  if (accoutnId) {
    try {
      const accountResult = await getMoolyAccount({
        accoutnId: accoutnId.toString(),
      });
      if (
        accountResult.success &&
        accountResult.data &&
        accountResult.data.token
      ) {
        apiToken = accountResult.data.token;
        console.log("Sử dụng token từ tài khoản:", apiToken);
      }
    } catch (error) {
      console.error("Lỗi khi lấy thông tin tài khoản:", error);
    }
  }

  // Khởi tạo Mastra client
  // const client = new MastraClient({
  //   baseUrl: process.env.MASTRA_SERVER_URL || "http://localhost:4111",
  // });

  // Chọn agent dựa trên type từ chatbot configuration
  let agentName = "ecommerceAgent"; // Default agent

  if (chatbotInfo?.type) {
    switch (chatbotInfo.type) {
      case 'sale':
      case 'sale_bot':
        agentName = "ecommerceAgent";
        console.log(`🤖 Sử dụng E-commerce Agent cho bot type: ${chatbotInfo.type}`);
        break;
      case 'rag_bot':
        agentName = "ragAgent";
        console.log(`🤖 Sử dụng RAG Agent cho bot type: ${chatbotInfo.type}`);
        break;
      default:
        console.log(`⚠️ Unknown bot type: ${chatbotInfo.type}, sử dụng E-commerce Agent mặc định`);
        agentName = "ecommerceAgent";
        break;
    }
  } else {
    console.log(`🤖 Không có thông tin type, sử dụng E-commerce Agent mặc định`);
  }

  // Lấy agent từ mastra
  const agent = mastra.getAgent(agentName as "ecommerceAgent" | "ragAgent");

  // Tạo context cho agent nếu có thông tin chatbot
  let customInstructions = undefined;
  if (chatbotInfo?.instruction) {
    customInstructions = chatbotInfo.instruction;
  }

  type ChatbotRuntimeContext = {
    bot_id: string;
    tenant_id: string;
    thread_id: string;
    resource_id: string;
    account_id: string;
    conversation_id: string;
  };

  const runtimeContext = new RuntimeContext<ChatbotRuntimeContext>();
  runtimeContext.set("bot_id", botIdToUse);
  runtimeContext.set("tenant_id", tenantIdToUse);
  runtimeContext.set("thread_id", threadIdToUse);
  runtimeContext.set("resource_id", resourceIdToUse);
  runtimeContext.set("account_id", accoutnId || "");
  runtimeContext.set("conversation_id", conversationId || "");

  // Retry logic với exponential backoff
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
    const startTime = Date.now();

    try {
      console.log(`🤖 Agent attempt ${attempt + 1}/${retryConfig.maxRetries + 1}`);

      // Chọn agent để sử dụng - nếu đã fail 2 lần, thử fallback với OpenAI
      let currentAgent = agent;

      // if (attempt >= 2) {
      //   try {
      //     console.log(`🔄 Switching to fallback OpenAI model for attempt ${attempt + 1}`);
      //     const { Agent } = await import("@mastra/core/agent");
      //     const { openai } = await import("@ai-sdk/openai");

      //     currentAgent = new Agent({
      //       name: "Fallback E-commerce Agent",
      //       instructions: customInstructions || "You are a helpful e-commerce assistant.",
      //       model: openai("gpt-4o-mini"),
      //       tools: await agent.getTools({ runtimeContext }),
      //     }) as any; // Type assertion để tránh conflict
      //   } catch (fallbackError) {
      //     console.warn("⚠️ Could not create fallback agent, using original:", fallbackError);
      //     currentAgent = agent;
      //   }
      // }
      console.log('------------')
      console.log(message)
      console.log(contactName)
      console.log('------------')
      // Gọi agent để tạo phản hồi
      const response = await currentAgent.generate(
        message,
        {
          threadId: threadIdToUse,
          resourceId: resourceIdToUse,
          temperature: 0.2,
          maxSteps: 5,
          maxRetries: 2, // Built-in retry của Mastra
          telemetry: {
            isEnabled: true,
            metadata: telemetryMetadata,
          },
          providerOptions: {
            google: {
              thinkingConfig: {
                thinkingBudget: 1000,
              },
            } satisfies GoogleGenerativeAIProviderOptions,
          },
          // Thêm system message nếu có custom instructions
          instructions: `
${customInstructions || ''}

Thông tin thêm:
${contactName ? "- Full name customer: " + contactName : ''}
- Current time: ${new Date().toLocaleString()}

## RESPONSE FORMAT
Luôn trả lời theo cấu trúc:

<thinking>
[Phân tích yêu cầu khách hàng, kiểm tra thông tin có sẵn, quyết định có cần dùng tool không]
</thinking>

<final_answer>
[Câu trả lời trực tiếp, tự nhiên, không đề cập đến "thông tin được cung cấp" hay "bối cảnh"]
</final_answer>

## COMMUNICATION GUIDELINES
Thực hiện chính xác các yêu cầu sau:
- Trả lời trực tiếp, ngắn gọn, tập trung vào vấn đề chính
- Sử dụng ngôn ngữ khách hàng đang dùng
- Xưng hô phù hợp với giới tính khách hàng dựa trên tên khách hàng
- Chủ động sử dụng tools khi cần thiết
- Ghi nhớ lịch sử hội thoại để tránh hỏi lại

Không được vi phạm các quy tắc sau:
- Không chào hỏi dài dòng
- Không lặp lại "ạ, dạ" quá nhiều
- Không đề cập "theo thông tin được cung cấp"
- Không hỏi lại thông tin đã biết
- Không được gửi trực tiếp link hình ảnh vào câu trả lời mà cần dùng tool "sendImagesTool" để gửi hình.

          `,
          runtimeContext,
          onStepFinish: async (step) => {
            console.log(`🔧 Step finished with ${step.toolCalls.length} tool calls`);

            // Xử lý tool humanHandoffTool - chuyển giao sang nhân viên
            if (step.toolCalls.find((item) => item.toolName === "humanHandoffTool")) {
              try {
                await fetch(
                  `https://app.mooly.vn/api/v1/accounts/${accoutnId}/conversations/${conversationId}/toggle_status`,
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                      api_access_token: apiToken,
                    },
                    body: JSON.stringify({
                      status: "open",
                    }),
                  }
                );
                console.log("✅ Đã chuyển giao sang nhân viên");
              } catch (error) {
                console.error("❌ Lỗi khi chuyển giao sang nhân viên:", error);
              }
            }

            // Xử lý tool detectSpamTool - phát hiện spam
            if (step.toolCalls.find((item) => item.toolName === "detectSpamTool")) {
              try {
                await fetch(
                  `https://app.mooly.vn/api/v1/accounts/${accoutnId}/conversations/${conversationId}/toggle_status`,
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                      api_access_token: apiToken,
                    },
                    body: JSON.stringify({
                      status: "open",
                    }),
                  }
                );
                console.log("✅ Đã phát hiện spam và chuyển sang nhân viên");
              } catch (error) {
                console.error("❌ Lỗi khi xử lý spam:", error);
              }
            }

            // Xử lý tool collectLeadTool - lưu thông tin lead vào Supabase
            if (step.toolCalls.find((item) => item.toolName === "collectLeadTool")) {
              const contactTool = step.toolCalls.find((item) => item.toolName === "collectLeadTool");
              const contactResult = step.toolResults.find(result => result.toolName === "collectLeadTool");

              if (contactTool?.args?.phone && contactResult?.result?.success) {
                try {
                  // Lấy thông tin từ runtime context
                  const tenant_id = runtimeContext.get("tenant_id");
                  const bot_id = runtimeContext.get("bot_id");

                  // Sử dụng contact name từ webhook hoặc fallback
                  const customerName = contactName || 'Khách hàng';

                  // Tạo lead data
                  const leadData = {
                    tenant_id: tenant_id,
                    full_name: customerName,
                    chatbot_id: bot_id || null,
                    phone: contactTool.args.phone,
                    conversation_id: conversationId,
                    source: 'chatbot',
                    status: 'new',
                    lead_data: {
                      phone: contactTool.args.phone,
                      collected_at: new Date().toISOString(),
                      conversation_id: conversationId,
                      chatbot_type: chatbotInfo?.type || 'rag',
                      customer_name: customerName
                    },
                    notes: `Lead được thu thập từ ${chatbotInfo?.type || 'RAG'} chatbot qua conversation ${conversationId}`,
                    last_contact_at: new Date().toISOString(),
                    lead_score: 50 // Điểm mặc định cho lead từ chatbot
                  };

                  const { data, error } = await supabaseAdmin
                    .from('chatbot_leads')
                    .insert(leadData)
                    .select();

                  if (error) {
                    console.error("❌ Lỗi khi lưu lead vào Supabase:", error);
                  } else {
                    console.log("✅ Đã lưu thông tin lead vào Supabase:", data[0]?.id);
                  }
                } catch (error) {
                  console.error("❌ Lỗi khi xử lý lưu lead:", error);
                }
              }
            }

            // Kiểm tra xem có tool sendImagesTool không để tránh gửi hình ảnh trùng lặp
            const hasSendImageTool = step.toolCalls.find((item) => item.toolName === "sendImagesTool" || item.toolName === "send_images");

            // Xử lý tool sendImagesTool - gửi danh sách hình ảnh
            if (hasSendImageTool) {
              const toolResult = step.toolResults.find(result => result.toolName === "sendImagesTool" || result.toolName === "send_images");

              if (toolResult?.args?.images && Array.isArray(toolResult.args.images)) {
                console.log(`📸 Đang gửi ${toolResult.args.images.length} hình ảnh từ sendImagesTool...`);

                for (const imageUrl of toolResult.args.images) {
                  if (imageUrl) {
                    try {
                      await fetch(
                        `https://app.mooly.vn/api/v1/accounts/${accoutnId}/conversations/${conversationId}/messages`,
                        {
                          method: "POST",
                          headers: {
                            "Content-Type": "application/json",
                            api_access_token: apiToken,
                          },
                          body: JSON.stringify({
                            content: "",
                            attachments: [{
                              "file_type": "image",
                              "external_url": imageUrl
                            }],
                            "private": false
                          }),
                        }
                      );
                      console.log(`📸 Đã gửi hình ảnh từ sendImagesTool`);
                    } catch (error) {
                      console.error(`❌ Lỗi khi gửi hình ảnh từ sendImagesTool:`, error);
                    }
                  }
                }
              }
            }
            // Chỉ xử lý các tool tìm kiếm sản phẩm nếu KHÔNG có sendImagesTool
            else {
              // Xử lý tool searchProductsTool - gửi hình ảnh sản phẩm tìm được
              if (step.toolCalls.find((item) => item.toolName === "searchProductsTool")) {
                const toolResult = step.toolResults.find(result => result.toolName === "searchProductsTool");

                if (toolResult?.result?.products && Array.isArray(toolResult.result.products)) {
                  console.log(`📦 Tìm thấy ${toolResult.result.products.length} sản phẩm, đang gửi hình ảnh...`);

                  // Gửi hình ảnh cho từng sản phẩm (tối đa 5 sản phẩm để tránh spam)
                  const productsToSend = toolResult.result.products.slice(0, 5);

                  for (const product of productsToSend) {
                    const imageUrl = product?.avatar || product?.images?.[0] || '';

                    if (imageUrl) {
                      try {
                        await fetch(
                          `https://app.mooly.vn/api/v1/accounts/${accoutnId}/conversations/${conversationId}/messages`,
                          {
                            method: "POST",
                            headers: {
                              "Content-Type": "application/json",
                              api_access_token: apiToken,
                            },
                            body: JSON.stringify({
                              content: "",
                              attachments: [{
                                "file_type": "image",
                                "external_url": imageUrl
                              }],
                              "private": false
                            }),
                          }
                        );
                        console.log(`📸 Đã gửi hình ảnh sản phẩm: ${product?.name || 'Unknown'}`);
                      } catch (error) {
                        console.error(`❌ Lỗi khi gửi hình ảnh sản phẩm ${product?.name}:`, error);
                      }
                    }
                  }
                }
              }

              // Xử lý tool getProductDetailsTool - gửi tất cả hình ảnh của sản phẩm
              if (step.toolCalls.find((item) => item.toolName === "getProductDetailsTool")) {
                const toolResult = step.toolResults.find(result => result.toolName === "getProductDetailsTool");

                if (toolResult?.result?.product_details) {
                  const product = toolResult.result.product_details;
                  console.log(`📋 Lấy chi tiết sản phẩm: ${product?.name || 'Unknown'}`);

                  // Tạo danh sách hình ảnh từ sản phẩm
                  const productImages: string[] = [];

                  // Thêm avatar nếu có
                  if (product.avatar) {
                    productImages.push(product.avatar);
                  }

                  // Thêm các hình ảnh khác nếu có
                  if (product.images && Array.isArray(product.images)) {
                    productImages.push(...product.images.filter((img: string) => img && img !== product.avatar));
                  }

                  // Gửi hình ảnh (tối đa 10 hình để tránh spam)
                  const imagesToSend = productImages.slice(0, 10);

                  if (imagesToSend.length > 0) {
                    console.log(`📸 Đang gửi ${imagesToSend.length} hình ảnh sản phẩm...`);

                    for (const imageUrl of imagesToSend) {
                      try {
                        await fetch(
                          `https://app.mooly.vn/api/v1/accounts/${accoutnId}/conversations/${conversationId}/messages`,
                          {
                            method: "POST",
                            headers: {
                              "Content-Type": "application/json",
                              api_access_token: apiToken,
                            },
                            body: JSON.stringify({
                              content: "",
                              attachments: [{
                                "file_type": "image",
                                "external_url": imageUrl
                              }],
                              "private": false
                            }),
                          }
                        );
                        console.log(`📸 Đã gửi hình ảnh chi tiết sản phẩm`);
                      } catch (error) {
                        console.error(`❌ Lỗi khi gửi hình ảnh chi tiết:`, error);
                      }
                    }
                  } else {
                    console.log(`⚠️ Không tìm thấy hình ảnh cho sản phẩm: ${product?.name}`);
                  }
                }
              }
            }
          },
        }
      );

      // Validate response sử dụng utility function
      const validatedResponse = validateAgentResponse(response);
console.log(response.reasoning)
console.log('----------')
console.log(response.text)
      const duration = Date.now() - startTime;
      logAgentMetrics(attempt + 1, true, duration, validatedResponse.usage);

      console.log(`✅ Agent response successful on attempt ${attempt + 1}`);
      return validatedResponse;

    } catch (error) {
      lastError = error as Error;
      const duration = Date.now() - startTime;

      logAgentMetrics(attempt + 1, false, duration, undefined, lastError);
      // console.error(`❌ Agent attempt ${attempt + 1} failed:`, error);

      // Kiểm tra xem lỗi có thể retry được không
      if (!isRetryableError(lastError)) {
        console.error(`💥 Non-retryable error encountered:`, lastError);
        break;
      }

      // Nếu đây là attempt cuối cùng, break
      if (attempt === retryConfig.maxRetries) {
        // console.error(`💥 All ${retryConfig.maxRetries + 1} attempts failed. Last error:`, lastError);
        break;
      }

      // Tính toán delay cho attempt tiếp theo với jitter
      const delay = calculateRetryDelay(attempt, retryConfig.baseDelay, retryConfig.maxDelay);
      console.log(`⏳ Waiting ${delay}ms before retry...`);
      await sleep(delay);
    }
  }

  // Nếu tất cả attempts đều fail, tạo fallback response
  console.error(`💥 Creating fallback response after ${retryConfig.maxRetries + 1} failed attempts`);
  const fallbackResponse = createFallbackResponse(lastError!);

  // Log fallback metrics
  logAgentMetrics(retryConfig.maxRetries + 1, false, 0, fallbackResponse.usage, lastError!);

  return fallbackResponse;
};
